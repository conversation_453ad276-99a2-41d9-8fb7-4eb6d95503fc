"use client";
import { Inter } from "next/font/google";
import "./globals.css";
import { ThemeStoreProvider } from "@/components/ThemeStoreProvider";
import { ThemeTransitionProvider } from "@/components/ThemeTransitionProvider";
import Navbar from "@/components/Navbar";
import Menu from "@/components/Menu";
import { AIInterface } from "@/components/ai-assistant";
import { Toaster } from "@/components/ui/sonner";
import ErrorBoundary from "@/components/ui/error-boundary";
import { useUIStore } from "@/store/uiStore";

const inter = Inter({ subsets: ["latin"] });

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { showSidebar, setShowSidebar, showAIAssistant, setShowAIAssistant } = useUIStore();

  return (
    <html lang="en" suppressHydrationWarning>
      <body
        className={`${inter.className} min-h-screen no-transition`}
        suppressHydrationWarning
      >
        <ThemeStoreProvider>
          <ThemeTransitionProvider>
            <Navbar />

            {/* Menu Overlay */}
            {showSidebar && (
              <>
                {/* Backdrop */}
                <div
                  className="fixed inset-0 bg-black/30 backdrop-blur-sm z-40"
                  onClick={() => setShowSidebar(false)}
                />

                {/* Menu */}
                <Menu />
              </>
            )}

            {/* AI Interface Dropdown */}
            <AIInterface
              isOpen={showAIAssistant}
              onClose={() => setShowAIAssistant(false)}
              userId="nilab"
            />

            <div className="min-h-screen w-full">
              <div className="flex-1">
                <ErrorBoundary>{children}</ErrorBoundary>
              </div>
            </div>
          </ThemeTransitionProvider>
          <Toaster />
        </ThemeStoreProvider>
      </body>
    </html>
  );
}
